# Unverified User Redirect Implementation

## Overview

This implementation resolves the issue where unverified users (who have signed up but not verified their email) could access authentication pages instead of being redirected to the verification page.

## Problem Statement

**Before**: 
- User signs up → gets redirected to `/verify?email=...`
- If user navigates to `/sign-in` or `/sign-up`, they can access those pages
- Unverified users don't have sessions, so they're treated as completely unauthenticated

**After**:
- User signs up → gets redirected to `/verify?email=...`
- If user navigates to `/sign-in` or `/sign-up`, they get redirected back to `/verify?email=...`
- System tracks unverified users and ensures they complete verification

## Implementation Details

### 1. Pending Verification Cookie

**File**: `apps/dashboard/src/features/auth/actions/sign-up.ts`

- Added a temporary cookie `pending-verification-email` during sign-up
- Cookie expires after 24 hours
- Contains the email of the user who needs verification
- Automatically cleared when verification completes

```typescript
// Set during sign-up
cookieStore.set({
  name: "pending-verification-email",
  value: normalizedEmail,
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
  httpOnly: true,
  secure: process.env.NODE_ENV === "production",
  sameSite: "lax",
  path: "/"
});
```

### 2. Enhanced AuthDAL Methods

**File**: `apps/dashboard/src/features/auth/dal/auth-dal.ts`

Added three new methods to track unverified users:

#### `checkUnverifiedUser(email?: string)`
- Checks if a specific email belongs to an unverified user
- Returns user email if unverified account exists

#### `checkRecentUnverifiedUsers()`
- Finds recent unverified users (within 24 hours)
- Fallback mechanism when no cookie is present
- Returns most recent unverified user email

#### `checkPendingVerificationCookie()`
- Checks for the pending verification cookie
- Validates that the email still corresponds to an unverified user
- Automatically cleans up cookie if user is verified

### 3. Updated Auth Page Access Logic

**File**: `apps/dashboard/src/features/auth/dal/auth-dal.ts`

Enhanced `checkAuthPageAccess()` method with layered approach:

1. **Check for authenticated users** → redirect based on status
2. **Check pending verification cookie** → redirect to verification
3. **Check recent unverified users** → redirect to verification
4. **Allow access to auth pages** → if no unverified users found

### 4. Cookie Cleanup

**File**: `apps/dashboard/src/features/auth/actions/verify-email-with-token.ts`

- Added automatic cookie cleanup when verification completes
- Ensures cookie doesn't persist after successful verification

## Test Scenarios

### Scenario 1: Normal Sign-up Flow
1. User visits `/sign-up`
2. User fills out form and submits
3. User account created with `emailVerified: null`
4. Pending verification cookie set
5. User redirected to `/verify?email=...`
6. **Expected**: User sees verification page

### Scenario 2: User Navigates Away After Sign-up
1. User completes sign-up (cookie set)
2. User navigates to `/sign-in` or `/sign-up`
3. AuthPageGuard checks for pending verification
4. **Expected**: User redirected back to `/verify?email=...`

### Scenario 3: User Tries to Access Protected Routes
1. User completes sign-up (no session created)
2. User tries to access `/onboarding/workspace`
3. WorkspaceOnboardingGuard redirects to `/sign-in`
4. AuthPageGuard detects pending verification
5. **Expected**: User redirected to `/verify?email=...`

### Scenario 4: User Completes Verification
1. User clicks verification link in email
2. Email verification completes successfully
3. Pending verification cookie cleared
4. Session created for user
5. **Expected**: User can access protected routes

### Scenario 5: Cookie Expires
1. User signs up but doesn't verify within 24 hours
2. Cookie expires automatically
3. Fallback mechanism checks for recent unverified users
4. **Expected**: User still redirected to verification if within 24 hours

## Security Considerations

1. **HttpOnly Cookie**: Prevents client-side JavaScript access
2. **Secure Flag**: Ensures HTTPS-only transmission in production
3. **SameSite Protection**: Prevents CSRF attacks
4. **Automatic Expiration**: 24-hour limit prevents indefinite tracking
5. **Database Validation**: Cookie email is validated against database
6. **Automatic Cleanup**: Cookie removed after successful verification

## Benefits

1. **Improved UX**: Users can't get "lost" after signing up
2. **Security**: Ensures email verification is completed
3. **Fallback Protection**: Multiple layers of unverified user detection
4. **Performance**: Minimal database queries with caching
5. **Maintainable**: Clean separation of concerns in DAL pattern

## Files Modified

1. `apps/dashboard/src/features/auth/actions/sign-up.ts` - Added cookie setting
2. `apps/dashboard/src/features/auth/actions/verify-email-with-token.ts` - Added cookie cleanup
3. `apps/dashboard/src/features/auth/dal/auth-dal.ts` - Enhanced unverified user detection
4. `apps/dashboard/src/features/auth/components/auth-page-guard.tsx` - Updated documentation
5. `apps/dashboard/src/features/auth/dal/README.md` - Updated documentation

## Integration

The implementation integrates seamlessly with the existing authentication system:

- **No breaking changes** to existing APIs
- **Backward compatible** with current user flows
- **Leverages existing** DAL pattern and route guards
- **Minimal performance impact** with React caching
