"use server";

import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { hashPassword } from "@repo/auth/password";
import { createOtpTokens } from "@repo/auth/verification";
import { prisma } from "@repo/database/client";
import { sendVerifyEmail } from "@repo/email/send-verify-email-address-email";
import { z } from "zod";

import { signUpSchema } from "../schemas/sign-up-schema";

// Sign up action to create a new user account
export async function signUp(formData: FormData) {
  // Parse and validate the form data
  const rawData = {
    firstName: formData.get("firstName"),
    lastName: formData.get("lastName"),
    email: formData.get("email"),
    password: formData.get("password")
  };

  const validatedData = signUpSchema.safeParse(rawData);

  if (!validatedData.success) {
    return {
      success: false,
      errors: validatedData.error.flatten().fieldErrors,
      message: "Invalid form data. Please check your inputs."
    };
  }

  const { firstName, lastName, email, password } = validatedData.data;
  const normalizedEmail = email.toLowerCase();

  try {
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: normalizedEmail },
      select: { id: true }
    });

    if (existingUser) {
      return {
        success: false,
        errors: {
          email: ["An account with this email already exists"]
        },
        message: "An account with this email already exists"
      };
    }

    // Hash the password
    const hashedPassword = await hashPassword(password);

    // Create the user in the database
    await prisma.user.create({
      data: {
        name: `${firstName} ${lastName}`,
        email: normalizedEmail,
        password: hashedPassword,
        onboardingStatus: "incomplete",
        createdAt: new Date(),
        updatedAt: new Date()
      },
      select: {
        id: true,
        email: true,
        name: true
      }
    });

    // Generate OTP tokens for email verification
    try {
      const { hashedOtp } = await createOtpTokens(normalizedEmail);

      // Get base URL from environment variables
      const baseUrl =
        process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

      // Send verification email
      await sendVerifyEmail({
        recipient: normalizedEmail,
        email: normalizedEmail,
        verifyLink: `${baseUrl}/verify-email/${hashedOtp}`
      });

      console.log(`Verification email sent to ${normalizedEmail}`);
    } catch (emailError) {
      console.error("Failed to send verification email:", emailError);
      // Don't fail the signup if email sending fails
      // User can request a new verification email later
    }

    // Set a temporary cookie to track pending verification
    // This helps redirect users back to verification if they navigate away
    const cookieStore = await cookies();
    cookieStore.set({
      name: "pending-verification-email",
      value: normalizedEmail,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      path: "/"
    });

    // Redirect to verification page
    redirect(`/verify?email=${encodeURIComponent(normalizedEmail)}`);
  } catch (error) {
    console.error("Sign up error:", error);

    // Check for specific database errors
    if (error instanceof Error) {
      if (error.message.includes("Unique constraint")) {
        return {
          success: false,
          errors: {
            email: ["An account with this email already exists"]
          },
          message: "An account with this email already exists"
        };
      }
    }

    return {
      success: false,
      errors: {},
      message: "An unexpected error occurred. Please try again."
    };
  }
}

type SignUpResult =
  | { success: true; redirectTo: string }
  | { success: false; errors: Record<string, string[]>; message: string };

// Alternative sign up function that accepts direct data instead of FormData
export async function signUpWithData(
  data: z.infer<typeof signUpSchema>
): Promise<SignUpResult> {
  const validatedData = signUpSchema.safeParse(data);

  if (!validatedData.success) {
    return {
      success: false,
      errors: validatedData.error.flatten().fieldErrors,
      message: "Invalid form data. Please check your inputs."
    };
  }

  const { firstName, lastName, email, password } = validatedData.data;
  const normalizedEmail = email.toLowerCase();

  try {
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: normalizedEmail },
      select: { id: true }
    });

    if (existingUser) {
      return {
        success: false,
        errors: {
          email: ["An account with this email already exists"]
        },
        message: "An account with this email already exists"
      };
    }

    // Hash the password
    const hashedPassword = await hashPassword(password);

    // Create the user in the database
    await prisma.user.create({
      data: {
        name: `${firstName} ${lastName}`,
        email: normalizedEmail,
        password: hashedPassword,
        onboardingStatus: "incomplete",
        createdAt: new Date(),
        updatedAt: new Date()
      },
      select: {
        id: true,
        email: true,
        name: true
      }
    });

    // Generate OTP tokens for email verification
    try {
      const { hashedOtp } = await createOtpTokens(normalizedEmail);

      // Get base URL from environment variables
      const baseUrl =
        process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

      // Send verification email
      await sendVerifyEmail({
        recipient: normalizedEmail,
        email: normalizedEmail,
        verifyLink: `${baseUrl}/verify-email/${hashedOtp}`
      });

      console.log(`Verification email sent to ${normalizedEmail}`);
    } catch (emailError) {
      console.error("Failed to send verification email:", emailError);
      // Don't fail the signup if email sending fails
      // User can request a new verification email later
    }

    // Set a temporary cookie to track pending verification
    // This helps redirect users back to verification if they navigate away
    const cookieStore = await cookies();
    cookieStore.set({
      name: "pending-verification-email",
      value: normalizedEmail,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      path: "/"
    });

    return {
      success: true,
      redirectTo: `/verify?email=${encodeURIComponent(normalizedEmail)}`
    };
  } catch (error) {
    console.error("Sign up error:", error);

    // Check for specific database errors
    if (error instanceof Error) {
      if (error.message.includes("Unique constraint")) {
        return {
          success: false,
          errors: {
            email: ["An account with this email already exists"]
          },
          message: "An account with this email already exists"
        };
      }
    }

    return {
      success: false,
      errors: {},
      message: "An unexpected error occurred. Please try again."
    };
  }
}
