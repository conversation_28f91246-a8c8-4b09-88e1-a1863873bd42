"use server";

import { cookies } from "next/headers";

/**
 * Server Action to clear the pending verification cookie
 * This must be a Server Action because cookies can only be modified in Server Actions or Route Handlers
 */
export async function clearPendingVerificationCookie(): Promise<void> {
  try {
    const cookieStore = await cookies();
    cookieStore.delete("pending-verification-email");
  } catch (error) {
    console.error("Error clearing pending verification cookie:", error);
    // Don't throw error - this is a cleanup operation
  }
}
