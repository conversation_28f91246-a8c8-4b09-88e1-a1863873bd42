"use server";

import { cookies } from "next/headers";
import { verifyPassword } from "@repo/auth/password";
import {
  generateSessionToken,
  getSessionExpiryFromNow
} from "@repo/auth/session";
import { MembershipStatus } from "@repo/database";
import { prisma } from "@repo/database/client";
import { z } from "zod";

import { signInSchema } from "../schemas/sign-in-schema";

type SignInResult =
  | { success: true; redirectTo: string }
  | { success: false; errors: Record<string, string[]>; message: string };

// Sign in action to authenticate existing users
export async function signIn(formData: FormData): Promise<SignInResult> {
  // Parse and validate the form data
  const rawData = {
    email: formData.get("email"),
    password: formData.get("password")
  };

  const validatedData = signInSchema.safeParse(rawData);

  if (!validatedData.success) {
    return {
      success: false,
      errors: validatedData.error.flatten().fieldErrors,
      message: "Invalid form data. Please check your inputs."
    };
  }

  const { email, password } = validatedData.data;
  const normalizedEmail = email.toLowerCase();

  try {
    // Find the user
    const user = await prisma.user.findUnique({
      where: { email: normalizedEmail },
      select: {
        id: true,
        password: true,
        email: true,
        emailVerified: true,
        name: true
      }
    });

    if (!user || !user.password || !user.email) {
      return {
        success: false,
        errors: {},
        message: "Invalid email or password"
      };
    }

    // Verify password
    const isCorrectPassword = await verifyPassword(password, user.password);
    if (!isCorrectPassword) {
      return {
        success: false,
        errors: {},
        message: "Invalid email or password"
      };
    }

    // Check if email is verified
    if (!user.emailVerified) {
      return {
        success: false,
        errors: {},
        message: "Please verify your email before signing in"
      };
    }

    // Generate Auth.js compatible session
    const sessionToken = generateSessionToken();
    const expires = getSessionExpiryFromNow();

    // Get user's oldest organization membership to set as active
    const oldestMembership = await prisma.membership.findFirst({
      where: {
        userId: user.id,
        membershipStatus: MembershipStatus.active
      },
      orderBy: { createdAt: "asc" },
      select: { organizationId: true }
    });

    // Create Auth.js database session with activeOrganizationId
    await prisma.session.create({
      data: {
        sessionToken,
        userId: user.id,
        expires,
        activeOrganizationId: oldestMembership?.organizationId || null
      }
    });

    // Update last login (mimics signIn event functionality)
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLogin: new Date() },
      select: { id: true }
    });

    // Set Auth.js compatible session cookie
    const cookieStore = await cookies();
    cookieStore.set({
      name: "next-auth.session-token",
      value: sessionToken,
      expires: expires,
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      path: "/"
    });

    // Clear pending verification cookie since user is now signed in
    cookieStore.delete("pending-verification-email");

    return {
      success: true,
      redirectTo: "/"
    };
  } catch (error) {
    console.error("Sign in error:", error);
    return {
      success: false,
      errors: {},
      message: "An unexpected error occurred. Please try again."
    };
  }
}

// Alternative sign in function that accepts direct data instead of FormData
export async function signInWithData(
  data: z.infer<typeof signInSchema>
): Promise<SignInResult> {
  const validatedData = signInSchema.safeParse(data);

  if (!validatedData.success) {
    return {
      success: false,
      errors: validatedData.error.flatten().fieldErrors,
      message: "Invalid form data. Please check your inputs."
    };
  }

  const { email, password } = validatedData.data;
  const normalizedEmail = email.toLowerCase();

  try {
    // Find the user
    const user = await prisma.user.findUnique({
      where: { email: normalizedEmail },
      select: {
        id: true,
        password: true,
        email: true,
        emailVerified: true,
        name: true
      }
    });

    if (!user || !user.password || !user.email) {
      return {
        success: false,
        errors: {},
        message: "Invalid email or password"
      };
    }

    // Verify password
    const isCorrectPassword = await verifyPassword(password, user.password);
    if (!isCorrectPassword) {
      return {
        success: false,
        errors: {},
        message: "Invalid email or password"
      };
    }

    // Check if email is verified
    if (!user.emailVerified) {
      return {
        success: false,
        errors: {},
        message: "Please verify your email before signing in"
      };
    }

    // Generate Auth.js compatible session
    const sessionToken = generateSessionToken();
    const expires = getSessionExpiryFromNow();

    // Get user's oldest organization membership to set as active
    const oldestMembership = await prisma.membership.findFirst({
      where: {
        userId: user.id,
        membershipStatus: MembershipStatus.active
      },
      orderBy: { createdAt: "asc" },
      select: { organizationId: true }
    });

    // Create Auth.js database session with activeOrganizationId
    await prisma.session.create({
      data: {
        sessionToken,
        userId: user.id,
        expires,
        activeOrganizationId: oldestMembership?.organizationId || null
      }
    });

    // Update last login (mimics signIn event functionality)
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLogin: new Date() },
      select: { id: true }
    });

    // Set Auth.js compatible session cookie
    const cookieStore = await cookies();
    cookieStore.set({
      name: "next-auth.session-token",
      value: sessionToken,
      expires: expires,
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      path: "/"
    });

    // Clear pending verification cookie since user is now signed in
    cookieStore.delete("pending-verification-email");

    return {
      success: true,
      redirectTo: "/"
    };
  } catch (error) {
    console.error("Sign in error:", error);
    return {
      success: false,
      errors: {},
      message: "An unexpected error occurred. Please try again."
    };
  }
}
