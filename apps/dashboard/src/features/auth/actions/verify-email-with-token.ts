"use server";

import { cookies } from "next/headers";
import { symmetricEncrypt } from "@repo/auth/encryption";
import { verifyEmail } from "@repo/auth/verification";
import { prisma } from "@repo/database/client";
import { sendWelcomeEmail } from "@repo/email/senders/send-welcome-email";
import { isAfter } from "date-fns";
import { z } from "zod";

const verifyEmailSchema = z.object({
  token: z.string().min(1, "Token is required")
});

type VerificationResult =
  | {
      success: true;
      redirectUrl: string;
      providerToken?: string;
    }
  | {
      success: false;
      redirectUrl: string;
    };

export async function verifyEmailWithToken(
  data: z.infer<typeof verifyEmailSchema>
): Promise<VerificationResult> {
  const validatedData = verifyEmailSchema.safeParse(data);

  if (!validatedData.success) {
    return {
      success: false,
      redirectUrl: "/sign-in?error=invalid-token"
    };
  }

  const { token } = validatedData.data;

  try {
    // Find the verification token directly using the hashed token
    const verificationToken = await prisma.verificationToken.findFirst({
      where: { token },
      select: { identifier: true, expires: true }
    });

    if (!verificationToken) {
      return {
        success: false,
        redirectUrl: "/sign-in?error=invalid-token"
      };
    }

    // Check if token has expired
    if (isAfter(new Date(), verificationToken.expires)) {
      return {
        success: false,
        redirectUrl: `/verify?email=${encodeURIComponent(verificationToken.identifier)}&error=expired`
      };
    }

    // Find the user
    const user = await prisma.user.findUnique({
      where: { email: verificationToken.identifier },
      select: {
        id: true,
        name: true,
        email: true,
        emailVerified: true,
        onboardingStatus: true
      }
    });

    if (!user) {
      return {
        success: false,
        redirectUrl: "/sign-in?error=user-not-found"
      };
    }

    // Check if email is already verified
    if (user.emailVerified) {
      // Clear the pending verification cookie since user is already verified
      try {
        const cookieStore = await cookies();
        cookieStore.delete("pending-verification-email");
      } catch (cookieError) {
        console.error(
          "Error clearing pending verification cookie:",
          cookieError
        );
        // Don't fail if cookie clearing fails
      }

      // For already verified users, redirect to sign-in with a message
      return {
        success: true,
        redirectUrl: "/sign-in?message=already-verified"
      };
    }

    // Verify the email
    await verifyEmail(verificationToken.identifier);

    // Clear the pending verification cookie since verification is complete
    try {
      const cookieStore = await cookies();
      cookieStore.delete("pending-verification-email");
    } catch (cookieError) {
      console.error("Error clearing pending verification cookie:", cookieError);
      // Don't fail verification if cookie clearing fails
    }

    // Fetch updated user data to ensure emailVerified is set
    const verifiedUser = await prisma.user.findUnique({
      where: { email: verificationToken.identifier },
      select: {
        id: true,
        name: true,
        email: true,
        emailVerified: true
      }
    });

    if (!verifiedUser?.emailVerified) {
      console.error("Email verification failed - emailVerified not set");
      return {
        success: false,
        redirectUrl: "/sign-in?error=verification-failed"
      };
    }

    // Send welcome email
    try {
      await sendWelcomeEmail({
        recipient: verifiedUser.email!,
        name: verifiedUser.name || "User",
        appName: "Centaly",
        getStartedLink:
          process.env.NEXT_PUBLIC_APP_URL + "/onboarding/workspace"
      });
    } catch (emailError) {
      console.error("Failed to send welcome email:", emailError);
      // Don't fail the verification if welcome email fails
    }

    // Create an encrypted token for the EmailVerification provider
    const key = process.env.AUTH_SECRET!;
    const providerToken = symmetricEncrypt(
      JSON.stringify({
        userId: verifiedUser.id,
        email: verifiedUser.email,
        purpose: "email-verification",
        expires: new Date(Date.now() + 5 * 60 * 1000).toISOString() // 5 minutes
      }),
      key
    );

    // Return success with the encrypted token for client-side authentication
    return {
      success: true,
      redirectUrl: "/onboarding/workspace",
      providerToken
    };
  } catch (error) {
    console.error("Email verification error:", error);
    return {
      success: false,
      redirectUrl: "/sign-in?error=verification-failed"
    };
  }
}
