"use client";

import { useEffect } from "react";
import { clearPendingVerificationCookie } from "@/src/features/auth/actions/clear-pending-verification";

interface CookieCleanupProps {
  /** Whether to clear the pending verification cookie */
  clearPendingVerification?: boolean;
}

/**
 * Client component that handles cookie cleanup
 * This is useful for clearing cookies when users land on auth pages
 * after verification attempts or other auth flows
 */
export function CookieCleanup({ clearPendingVerification = false }: CookieCleanupProps) {
  useEffect(() => {
    const cleanup = async () => {
      if (clearPendingVerification) {
        try {
          await clearPendingVerificationCookie();
        } catch (error) {
          console.error("Error clearing pending verification cookie:", error);
          // Don't fail the page load if cookie clearing fails
        }
      }
    };

    cleanup();
  }, [clearPendingVerification]);

  // This component doesn't render anything
  return null;
}
