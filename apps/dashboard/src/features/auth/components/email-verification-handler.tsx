"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { clearPendingVerificationCookie } from "@/src/features/auth/actions/clear-pending-verification";
import { Card } from "@repo/ui/components/card";
import { Spinner } from "@repo/ui/components/spinner";

interface EmailVerificationHandlerProps {
  providerToken?: string;
}

export default function EmailVerificationHandler({
  providerToken
}: EmailVerificationHandlerProps) {
  const [status, setStatus] = useState<
    "processing" | "signing-in" | "redirecting" | "error"
  >("processing");
  const [errorMessage, setErrorMessage] = useState<string>("");
  const router = useRouter();

  useEffect(() => {
    const handleSignIn = async () => {
      if (!providerToken) {
        console.error("No provider token available for authentication");
        setStatus("error");
        setErrorMessage("Authentication token not available");
        return;
      }

      try {
        setStatus("signing-in");

        const response = await fetch(
          "http://localhost:3001/api/auth/signin/email-verification",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/x-www-form-urlencoded"
            },
            body: new URLSearchParams({
              token: providerToken,
              callbackUrl: "/onboarding/workspace"
            }),
            credentials: "include" // Required for cross-domain cookies
          }
        );

        if (response.ok) {
          const result = await response.json();

          setStatus("redirecting");

          // Clear the pending verification cookie since sign-in was successful
          try {
            await clearPendingVerificationCookie();
          } catch (error) {
            console.error("Error clearing pending verification cookie:", error);
            // Don't fail the flow if cookie clearing fails
          }

          // Use the callback URL from the response
          const redirectUrl = result.callbackUrl || "/onboarding/workspace";

          setTimeout(() => {
            router.push(redirectUrl);
          }, 1000);
        } else {
          const errorResponse = await response.json();
          console.error("Sign-in failed:", errorResponse);
          setStatus("error");
          setErrorMessage(
            errorResponse.error || "Failed to sign in automatically"
          );
        }
      } catch (error) {
        console.error("Error during auto sign-in:", error);
        setStatus("error");
        setErrorMessage("An unexpected error occurred during sign-in");
      }
    };

    handleSignIn();
  }, [providerToken, router]);

  return (
    <main className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md p-8 text-center space-y-6">
        <div className="space-y-4">
          {status === "error" ? (
            <div className="text-center">
              <p className="text-sm text-destructive">
                Something went wrong, please try again
              </p>
            </div>
          ) : (
            <>
              <div className="flex items-center justify-center gap-2">
                <Spinner size="small" />
                <div className="">Taking you to onboarding...</div>
              </div>
            </>
          )}
        </div>
      </Card>
    </main>
  );
}
