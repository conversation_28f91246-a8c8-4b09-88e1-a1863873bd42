import { cache } from "react";
import { cookies } from "next/headers";
import { getBackendSession } from "@/src/features/auth/utils/session";
import {
  getActiveOrganization,
  getUserOrganizations
} from "@/src/lib/api/onboarding";
import { prisma } from "@repo/database/client";

/**
 * Data Transfer Objects (DTOs) for session data
 * These interfaces define the minimal data needed for authorization decisions
 */
export interface AuthUserDTO {
  id: string;
  email: string;
  name: string;
  emailVerified: boolean;
  onboardingStatus: "incomplete" | "workspace" | "invite" | "complete";
  defaultWorkspace: string | null;
}

export interface OrganizationDTO {
  id: string;
  name: string;
  slug: string;
  userRole: "viewer" | "contributor" | "admin" | "owner";
}

export interface AuthSessionDTO {
  user: AuthUserDTO;
  organizations: OrganizationDTO[];
  activeOrganization: OrganizationDTO | null;
}

/**
 * Authorization result indicating what action should be taken
 */
export interface AuthorizationResult {
  isAuthenticated: boolean;
  shouldRedirect: boolean;
  redirectTo?: string;
  reason?: string;
}

/**
 * Data Access Layer for Authentication
 * Centralizes all authentication and authorization logic
 */
export class AuthDAL {
  /**
   * Get current session with all necessary data for authorization decisions
   * Uses React cache to prevent duplicate API calls within the same request
   */
  static getSessionData = cache(async (): Promise<AuthSessionDTO | null> => {
    try {
      // Get basic session data
      const session = await getBackendSession();
      if (!session?.user) {
        return null;
      }

      // Transform session user to DTO format
      const user: AuthUserDTO = {
        id: session.user.id,
        email: session.user.email,
        name: session.user.name,
        emailVerified: !!session.user.emailVerified,
        onboardingStatus: session.user.onboardingStatus || "incomplete",
        defaultWorkspace: session.user.defaultWorkspace || null
      };

      // Initialize with empty data - we'll fetch organizations separately if needed
      const organizations: OrganizationDTO[] = [];
      const activeOrganization: OrganizationDTO | null = null;

      return {
        user,
        organizations,
        activeOrganization
      };
    } catch (error) {
      console.error("Error in AuthDAL.getSessionData:", error);
      return null;
    }
  });

  /**
   * Get session data with organization information
   * Only fetches organization data when explicitly needed and user is authenticated
   */
  static getSessionDataWithOrganizations = cache(
    async (): Promise<AuthSessionDTO | null> => {
      try {
        // First get basic session data
        const sessionData = await this.getSessionData();
        if (!sessionData) {
          return null;
        }

        // Only fetch organization data if user is authenticated and has completed basic onboarding
        if (sessionData.user.id && sessionData.user.emailVerified) {
          try {
            // Fetch organizations data with proper error handling
            const [orgsResponse, activeOrgResponse] = await Promise.allSettled([
              getUserOrganizations(),
              getActiveOrganization()
            ]);

            if (
              orgsResponse.status === "fulfilled" &&
              orgsResponse.value?.organizations
            ) {
              sessionData.organizations = orgsResponse.value.organizations.map(
                (org: {
                  id: string;
                  name: string;
                  slug: string;
                  userRole: string;
                }) => ({
                  id: org.id,
                  name: org.name,
                  slug: org.slug,
                  userRole: org.userRole
                })
              );
            } else if (orgsResponse.status === "rejected") {
              // Don't log 401 errors as warnings since they're expected for some auth states
              const error = orgsResponse.reason;
              if (error?.message?.includes("401")) {
                console.debug(
                  "User not authorized to fetch organizations (expected during auth flow)"
                );
              } else {
                console.warn("Failed to fetch user organizations:", error);
              }
            }

            if (
              activeOrgResponse.status === "fulfilled" &&
              activeOrgResponse.value?.activeOrganization
            ) {
              const activeOrg = activeOrgResponse.value.activeOrganization;
              sessionData.activeOrganization = {
                id: activeOrg.id,
                name: activeOrg.name,
                slug: activeOrg.slug,
                userRole: activeOrg.userRole
              };
            } else if (activeOrgResponse.status === "rejected") {
              // Don't log 401 errors as warnings since they're expected for some auth states
              const error = activeOrgResponse.reason;
              if (error?.message?.includes("401")) {
                console.debug(
                  "User not authorized to fetch active organization (expected during auth flow)"
                );
              } else {
                console.warn("Failed to fetch active organization:", error);
              }
            }
          } catch (error) {
            // Log error but don't fail the entire session check
            console.warn("Unexpected error fetching organization data:", error);
          }
        }

        return sessionData;
      } catch (error) {
        console.error(
          "Error in AuthDAL.getSessionDataWithOrganizations:",
          error
        );
        return null;
      }
    }
  );

  /**
   * Check if there's an unverified user account for the given email
   * This helps identify users who have signed up but not verified their email
   */
  static async checkUnverifiedUser(email?: string): Promise<{
    hasUnverifiedUser: boolean;
    email?: string;
  }> {
    if (!email) {
      return { hasUnverifiedUser: false };
    }

    try {
      const user = await prisma.user.findUnique({
        where: { email: email.toLowerCase() },
        select: {
          id: true,
          email: true,
          emailVerified: true
        }
      });

      // User exists and email is not verified
      if (user && !user.emailVerified && user.email) {
        return {
          hasUnverifiedUser: true,
          email: user.email
        };
      }

      return { hasUnverifiedUser: false };
    } catch (error) {
      console.error("Error checking unverified user:", error);
      return { hasUnverifiedUser: false };
    }
  }

  /**
   * Check if there are any recent unverified users (within last 24 hours)
   * This helps identify if someone recently signed up but hasn't verified
   */
  static async checkRecentUnverifiedUsers(): Promise<{
    hasRecentUnverifiedUsers: boolean;
    mostRecentEmail?: string;
  }> {
    try {
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

      const recentUnverifiedUser = await prisma.user.findFirst({
        where: {
          emailVerified: null,
          createdAt: {
            gte: twentyFourHoursAgo
          }
        },
        select: {
          email: true,
          createdAt: true
        },
        orderBy: {
          createdAt: "desc"
        }
      });

      if (recentUnverifiedUser && recentUnverifiedUser.email) {
        return {
          hasRecentUnverifiedUsers: true,
          mostRecentEmail: recentUnverifiedUser.email
        };
      }

      return { hasRecentUnverifiedUsers: false };
    } catch (error) {
      console.error("Error checking recent unverified users:", error);
      return { hasRecentUnverifiedUsers: false };
    }
  }

  /**
   * Check for pending verification email from cookie
   * This helps track users who signed up but navigated away from verification
   */
  static async checkPendingVerificationCookie(): Promise<{
    hasPendingVerification: boolean;
    email?: string;
  }> {
    try {
      const cookieStore = await cookies();
      const pendingEmail = cookieStore.get("pending-verification-email");

      if (pendingEmail?.value) {
        // Verify this email still corresponds to an unverified user
        const unverifiedCheck = await this.checkUnverifiedUser(
          pendingEmail.value
        );
        if (unverifiedCheck.hasUnverifiedUser) {
          return {
            hasPendingVerification: true,
            email: pendingEmail.value
          };
        } else {
          // Email is verified or doesn't exist, clear the cookie
          cookieStore.delete("pending-verification-email");
        }
      }

      return { hasPendingVerification: false };
    } catch (error) {
      console.error("Error checking pending verification cookie:", error);
      return { hasPendingVerification: false };
    }
  }

  /**
   * Check if user should be redirected from authentication pages
   * Implements the authorization logic for sign-in/sign-up pages
   */
  static async checkAuthPageAccess(
    email?: string
  ): Promise<AuthorizationResult> {
    const sessionData = await this.getSessionData();

    // If no session, check if there's an unverified user account
    if (!sessionData) {
      // First, check if a specific email was provided and has an unverified account
      if (email) {
        const unverifiedCheck = await this.checkUnverifiedUser(email);
        if (unverifiedCheck.hasUnverifiedUser && unverifiedCheck.email) {
          return {
            isAuthenticated: false,
            shouldRedirect: true,
            redirectTo: `/verify?email=${encodeURIComponent(unverifiedCheck.email)}`,
            reason:
              "User has unverified account and should complete email verification"
          };
        }
      }

      // Check for pending verification cookie
      const pendingVerificationCheck =
        await this.checkPendingVerificationCookie();
      if (
        pendingVerificationCheck.hasPendingVerification &&
        pendingVerificationCheck.email
      ) {
        return {
          isAuthenticated: false,
          shouldRedirect: true,
          redirectTo: `/verify?email=${encodeURIComponent(pendingVerificationCheck.email)}`,
          reason:
            "Pending email verification found, redirecting to complete verification"
        };
      }

      // If no specific email or no unverified account for that email,
      // check if there are any recent unverified users (within 24 hours)
      // This helps catch users who signed up recently but navigated away
      const recentUnverifiedCheck = await this.checkRecentUnverifiedUsers();
      if (
        recentUnverifiedCheck.hasRecentUnverifiedUsers &&
        recentUnverifiedCheck.mostRecentEmail
      ) {
        return {
          isAuthenticated: false,
          shouldRedirect: true,
          redirectTo: `/verify?email=${encodeURIComponent(recentUnverifiedCheck.mostRecentEmail)}`,
          reason:
            "Recent unverified account found, redirecting to complete email verification"
        };
      }

      return {
        isAuthenticated: false,
        shouldRedirect: false
      };
    }

    const { user } = sessionData;

    // User is authenticated, determine where to redirect them
    // We'll fetch organization data only if needed for the redirect logic
    const redirectTo = await this.determineRedirectDestinationForUser(user);

    return {
      isAuthenticated: true,
      shouldRedirect: true,
      redirectTo,
      reason: "User is already authenticated"
    };
  }

  /**
   * Determine the appropriate onboarding redirect based on user's onboarding status
   * Returns null if user has completed onboarding
   */
  private static getOnboardingRedirect(
    onboardingStatus: "incomplete" | "workspace" | "invite" | "complete"
  ): string | null {
    switch (onboardingStatus) {
      case "incomplete":
        // User hasn't started onboarding - redirect to workspace creation
        return "/onboarding/workspace";

      case "workspace":
        // User completed workspace creation but hasn't done invites - redirect to invite step
        return "/onboarding/invite";

      case "invite":
        // User completed invites but onboarding isn't marked complete - redirect to invite step
        // This handles edge cases where the status wasn't properly updated to "complete"
        return "/onboarding/invite";

      case "complete":
        // User has completed all onboarding steps - no redirect needed
        return null;

      default:
        // Fallback for any unexpected status - redirect to workspace creation
        console.warn(
          `Unexpected onboarding status: ${onboardingStatus}, redirecting to workspace`
        );
        return "/onboarding/workspace";
    }
  }

  /**
   * Determine the appropriate redirect destination for authenticated users
   * Implements the priority logic specified in requirements
   * This version fetches organization data only when needed
   */
  private static async determineRedirectDestinationForUser(
    user: AuthUserDTO
  ): Promise<string> {
    // Priority 1: Handle granular onboarding states
    const onboardingRedirect = this.getOnboardingRedirect(
      user.onboardingStatus
    );
    if (onboardingRedirect) {
      return onboardingRedirect;
    }

    // Priority 2: If user has a defaultWorkspace, redirect to it
    if (user.defaultWorkspace) {
      return `/${user.defaultWorkspace}/home`;
    }

    // Priority 3: If email is not verified, redirect to verification
    if (!user.emailVerified) {
      return "/verify";
    }

    // Now we need to check organizations, so fetch the data
    try {
      const sessionDataWithOrgs = await this.getSessionDataWithOrganizations();
      if (sessionDataWithOrgs) {
        const { organizations, activeOrganization } = sessionDataWithOrgs;

        // Priority 4: If user has no organizations, redirect to onboarding
        if (organizations.length === 0) {
          return "/onboarding/workspace";
        }

        // Priority 5: If user has an active organization, redirect to it
        if (activeOrganization) {
          return `/${activeOrganization.slug}/home`;
        }

        // Priority 6: If user has organizations but no active, use the first one
        if (organizations.length > 0) {
          // Use the oldest organization (first in the array as they're ordered by createdAt asc)
          return `/${organizations[0]?.slug}/home`;
        }
      }
    } catch (error) {
      console.warn(
        "Failed to fetch organization data for redirect, using fallback:",
        error
      );
    }

    // Fallback: redirect to root
    return "/";
  }

  /**
   * Utility function to check if user needs email verification
   */
  static async requiresEmailVerification(): Promise<boolean> {
    const sessionData = await this.getSessionData();
    return sessionData ? !sessionData.user.emailVerified : false;
  }

  /**
   * Utility function to check if user has completed onboarding
   */
  static async hasCompletedOnboarding(): Promise<boolean> {
    const sessionData = await this.getSessionData();
    return sessionData
      ? sessionData.user.onboardingStatus === "complete"
      : false;
  }

  /**
   * Utility function to check if user needs to complete workspace creation
   */
  static async needsWorkspaceCreation(): Promise<boolean> {
    const sessionData = await this.getSessionData();
    return sessionData
      ? sessionData.user.onboardingStatus === "incomplete"
      : false;
  }

  /**
   * Utility function to check if user needs to complete member invitations
   */
  static async needsMemberInvitation(): Promise<boolean> {
    const sessionData = await this.getSessionData();
    return sessionData
      ? sessionData.user.onboardingStatus === "workspace" ||
          sessionData.user.onboardingStatus === "invite"
      : false;
  }

  /**
   * Utility function to get user's organizations count
   */
  static async getOrganizationCount(): Promise<number> {
    const sessionData = await this.getSessionDataWithOrganizations();
    return sessionData ? sessionData.organizations.length : 0;
  }

  /**
   * Check if user should be redirected from workspace onboarding page
   * Access Requirements: User must be authenticated AND email verified
   * Redirect Logic:
   * - If user has already completed onboarding → redirect to home page
   * - If user already has organizations → redirect to home page
   */
  static async checkWorkspaceOnboardingAccess(): Promise<AuthorizationResult> {
    const sessionData = await this.getSessionData();

    // If no session, user cannot access onboarding pages
    if (!sessionData) {
      return {
        isAuthenticated: false,
        shouldRedirect: true,
        redirectTo: "/sign-in",
        reason: "User must be authenticated to access onboarding"
      };
    }

    const { user } = sessionData;

    // Check if email is verified
    if (!user.emailVerified) {
      return {
        isAuthenticated: true,
        shouldRedirect: true,
        redirectTo: "/verify",
        reason: "Email verification required for onboarding"
      };
    }

    // Check if user has already completed onboarding
    if (user.onboardingStatus === "complete") {
      const redirectTo = await this.determineRedirectDestinationForUser(user);
      return {
        isAuthenticated: true,
        shouldRedirect: true,
        redirectTo,
        reason: "User has already completed onboarding"
      };
    }

    // Check if user has already progressed past workspace creation
    // If status is "workspace", "invite", they should go to invite page instead
    if (
      user.onboardingStatus === "workspace" ||
      user.onboardingStatus === "invite"
    ) {
      return {
        isAuthenticated: true,
        shouldRedirect: true,
        redirectTo: "/onboarding/invite",
        reason:
          "User has already created a workspace, should complete invite step"
      };
    }

    // For users with "incomplete" status, check if they already have organizations
    // (this handles edge cases where status wasn't properly updated)
    try {
      const sessionDataWithOrgs = await this.getSessionDataWithOrganizations();
      if (sessionDataWithOrgs && sessionDataWithOrgs.organizations.length > 0) {
        // User has organizations but incomplete status - redirect to invite step
        return {
          isAuthenticated: true,
          shouldRedirect: true,
          redirectTo: "/onboarding/invite",
          reason:
            "User already has organizations but needs to complete onboarding"
        };
      }
    } catch (error) {
      // Log error but don't fail the check - allow access if we can't determine org status
      // This handles the case where a user genuinely needs to create their first workspace
      console.warn(
        "Failed to check organization status for workspace onboarding:",
        error
      );
    }

    // User is authenticated, email verified, and needs workspace creation
    return {
      isAuthenticated: true,
      shouldRedirect: false
    };
  }

  /**
   * Check if user should be redirected from invite onboarding page
   * Access Requirements: User must be authenticated AND email verified AND have a workspace
   * Redirect Logic:
   * - If user has no workspace → redirect to /onboarding/workspace
   * - If user has completed onboarding → redirect to home page
   */
  static async checkInviteOnboardingAccess(): Promise<AuthorizationResult> {
    const sessionData = await this.getSessionData();

    // If no session, user cannot access onboarding pages
    if (!sessionData) {
      return {
        isAuthenticated: false,
        shouldRedirect: true,
        redirectTo: "/sign-in",
        reason: "User must be authenticated to access onboarding"
      };
    }

    const { user } = sessionData;

    // Check if email is verified
    if (!user.emailVerified) {
      return {
        isAuthenticated: true,
        shouldRedirect: true,
        redirectTo: "/verify",
        reason: "Email verification required for onboarding"
      };
    }

    // Check if user has completed onboarding
    if (user.onboardingStatus === "complete") {
      const redirectTo = await this.determineRedirectDestinationForUser(user);
      return {
        isAuthenticated: true,
        shouldRedirect: true,
        redirectTo,
        reason: "User has already completed onboarding"
      };
    }

    // Special case: If user's onboarding status is "invite" or "workspace",
    // they likely just created a workspace and should be allowed to access invite page
    // This handles the timing issue where organization APIs might temporarily fail
    if (
      user.onboardingStatus === "invite" ||
      user.onboardingStatus === "workspace"
    ) {
      console.debug(
        `User has onboarding status "${user.onboardingStatus}", allowing access to invite page`
      );
      return {
        isAuthenticated: true,
        shouldRedirect: false
      };
    }

    // For users with "incomplete" status, check if they have organizations
    try {
      const sessionDataWithOrgs = await this.getSessionDataWithOrganizations();
      if (
        !sessionDataWithOrgs ||
        sessionDataWithOrgs.organizations.length === 0
      ) {
        return {
          isAuthenticated: true,
          shouldRedirect: true,
          redirectTo: "/onboarding/workspace",
          reason: "User needs to create a workspace first"
        };
      }
    } catch (error) {
      // If we can't determine org status, redirect to workspace creation to be safe
      console.warn(
        "Failed to check organization status for invite onboarding:",
        error
      );

      return {
        isAuthenticated: true,
        shouldRedirect: true,
        redirectTo: "/onboarding/workspace",
        reason: "Unable to verify workspace status"
      };
    }

    // User is authenticated, email verified, and has a workspace
    return {
      isAuthenticated: true,
      shouldRedirect: false
    };
  }
}
