import * as React from "react";
import { <PERSON>ada<PERSON> } from "next";
import { AuthPageGuard } from "@/src/features/auth/components/auth-page-guard";
import { <PERSON>ieCleanup } from "@/src/features/auth/components/cookie-cleanup";
import SignInForm from "@/src/features/auth/components/sign-in-form";
import { AuthContainer } from "@repo/ui/components/auth-container";

export const metadata: Metadata = {
  title: "Sign In | Centaly"
};

export default async function SignInPage() {
  return (
    <AuthPageGuard>
      <CookieCleanup clearPendingVerification={true} />
      <AuthContainer maxWidth="md">
        <SignInForm />
      </AuthContainer>
    </AuthPageGuard>
  );
}
